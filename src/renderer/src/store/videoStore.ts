import { create } from 'zustand'
import { toast } from 'sonner'

interface VideoFile {
	path: string
	status?: number | string // 处理状态：数字表示进度百分比，字符串表示状态描述
}

// 多文件显示模式
export type VideoDisplayMode = 'table' | 'list-detail' | 'grid'

interface VideoStore {
	// 状态
	files: VideoFile[]
	currentFile: string | null
	isProcessing: boolean
	displayMode: VideoDisplayMode

	// 操作
	addFiles: (filePaths: string[]) => void
	removeFile: (filePath: string) => void
	clearFiles: () => void
	setCurrentFile: (filePath: string | null) => void
	setProcessing: (isProcessing: boolean) => void
	setFileStatus: (filePath: string, status: number | string) => void
	resetFileStatus: () => void
	setDisplayMode: (mode: VideoDisplayMode) => void
}

export const useVideoStore = create<VideoStore>((set) => ({
	// 初始状态
	files: [],
	currentFile: null,
	isProcessing: false,
	displayMode: 'table',

	// 操作
	addFiles: (filePaths) => set((state) => {
		// 检查重复文件
		const duplicateFiles: string[] = []
		const newFiles: VideoFile[] = []

		filePaths.forEach((filePath) => {
			if (state.files.some(file => file.path === filePath)) {
				duplicateFiles.push(filePath)
			} else {
				newFiles.push({ path: filePath })
			}
		})

		// 显示提示信息
		if (duplicateFiles.length > 0) {
			const fileNames = duplicateFiles.map(file => file.split('/').pop() || file.split('\\').pop())
			toast.info(`已跳过${duplicateFiles.length}个重复文件: ${fileNames.join(', ')}`)
		}

		return {
			files: [...state.files, ...newFiles],
			// 如果当前没有选中文件，则选中第一个新文件
			currentFile: state.currentFile || (newFiles.length > 0 ? newFiles[0].path : null)
		}
	}),

	removeFile: (filePath) => set((state) => {
		// 如果正在处理该文件，不允许移除
		if (state.isProcessing && state.currentFile === filePath) {
			toast.error('无法移除正在处理的文件')
			return state
		}

		const newFiles = state.files.filter(file => file.path !== filePath)
		let newCurrentFile = state.currentFile

		// 如果删除的是当前选中的文件，则重新选择
		if (state.currentFile === filePath) {
			newCurrentFile = newFiles.length > 0 ? newFiles[0].path : null
		}

		return {
			files: newFiles,
			currentFile: newCurrentFile
		}
	}),

	clearFiles: () => {
		console.log('清空 videoStore');
		return set({ files: [], currentFile: null });
	},

	setCurrentFile: (filePath) => set({ currentFile: filePath }),

	setProcessing: (isProcessing) => set({ isProcessing }),

	setFileStatus: (filePath, status) => set((state) => ({
		files: state.files.map(file =>
			file.path === filePath ? { ...file, status } : file
		)
	})),

	resetFileStatus: () => set((state) => ({
		files: state.files.map(file => ({ path: file.path }))
	})),

	setDisplayMode: (mode) => set({ displayMode: mode })
}))
